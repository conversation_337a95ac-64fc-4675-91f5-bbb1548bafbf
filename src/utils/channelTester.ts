import type { M3UChannel } from '../types';

export interface ChannelTestResult {
  channel: M3UChannel;
  status: 'healthy' | 'slow' | 'error' | 'timeout' | 'cors' | 'auth';
  responseTime: number;
  error?: string;
  details?: {
    httpStatus?: number;
    contentType?: string;
    contentLength?: number;
    serverInfo?: string;
    isHLS?: boolean;
    segmentCount?: number;
    resolution?: string;
    bitrate?: number;
  };
}

export class ChannelTester {
  private static readonly TIMEOUT_MS = 10000;
  private static readonly SLOW_THRESHOLD_MS = 3000;

  /**
   * 測試單個頻道的健康狀態
   */
  static async testChannel(channel: M3UChannel): Promise<ChannelTestResult> {
    const startTime = Date.now();
    
    try {
      // 使用 AbortController 來控制超時
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT_MS);

      // 首先嘗試 HEAD 請求來檢查基本可用性
      const response = await fetch(channel.url, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/vnd.apple.mpegurl,application/x-mpegurl,video/mp4,*/*',
          'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        mode: 'cors',
        credentials: 'omit'
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      // 收集響應詳細資訊
      const details = {
        httpStatus: response.status,
        contentType: response.headers.get('content-type') || undefined,
        contentLength: parseInt(response.headers.get('content-length') || '0') || undefined,
        serverInfo: response.headers.get('server') || undefined,
        isHLS: this.isHLSContent(response.headers.get('content-type'))
      };

      // 根據 HTTP 狀態碼判斷
      if (!response.ok) {
        let status: ChannelTestResult['status'] = 'error';
        let error = `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401 || response.status === 403) {
          status = 'auth';
          error = '認證失敗或權限不足';
        } else if (response.status === 404) {
          error = '頻道不存在或已移除';
        } else if (response.status >= 500) {
          error = '服務器內部錯誤';
        }

        return {
          channel,
          status,
          responseTime,
          error,
          details
        };
      }

      // 根據響應時間判斷健康狀態
      let status: ChannelTestResult['status'];
      if (responseTime > this.SLOW_THRESHOLD_MS) {
        status = 'slow';
      } else {
        status = 'healthy';
      }

      return {
        channel,
        status,
        responseTime,
        details
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            channel,
            status: 'timeout',
            responseTime,
            error: '連接超時'
          };
        }

        // 檢查是否為 CORS 錯誤
        if (error.message.includes('CORS') || error.message.includes('cross-origin')) {
          return {
            channel,
            status: 'cors',
            responseTime,
            error: 'CORS 政策阻止存取'
          };
        }

        // 檢查是否為網路錯誤
        if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
          return {
            channel,
            status: 'error',
            responseTime,
            error: '網路連接失敗'
          };
        }

        return {
          channel,
          status: 'error',
          responseTime,
          error: error.message
        };
      }

      return {
        channel,
        status: 'error',
        responseTime,
        error: '未知錯誤'
      };
    }
  }

  /**
   * 批次測試多個頻道
   */
  static async testChannels(
    channels: M3UChannel[],
    onProgress?: (current: number, total: number, channel: M3UChannel) => void
  ): Promise<ChannelTestResult[]> {
    const results: ChannelTestResult[] = [];
    
    for (let i = 0; i < channels.length; i++) {
      const channel = channels[i];
      
      if (onProgress) {
        onProgress(i + 1, channels.length, channel);
      }

      const result = await this.testChannel(channel);
      results.push(result);

      // 小延遲避免過度請求
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return results;
  }

  /**
   * 分析測試結果並提供建議
   */
  static analyzeResults(results: ChannelTestResult[]): {
    summary: {
      total: number;
      healthy: number;
      slow: number;
      error: number;
      timeout: number;
      cors: number;
      auth: number;
    };
    recommendations: string[];
    problematicChannels: ChannelTestResult[];
  } {
    const summary = {
      total: results.length,
      healthy: results.filter(r => r.status === 'healthy').length,
      slow: results.filter(r => r.status === 'slow').length,
      error: results.filter(r => r.status === 'error').length,
      timeout: results.filter(r => r.status === 'timeout').length,
      cors: results.filter(r => r.status === 'cors').length,
      auth: results.filter(r => r.status === 'auth').length,
    };

    const recommendations: string[] = [];
    const problematicChannels = results.filter(r => r.status !== 'healthy');

    // 生成建議
    if (summary.cors > 0) {
      recommendations.push(`${summary.cors} 個頻道受到 CORS 政策限制，建議使用代理服務器或瀏覽器擴展`);
    }

    if (summary.auth > 0) {
      recommendations.push(`${summary.auth} 個頻道需要認證，請檢查 token 或登入狀態`);
    }

    if (summary.timeout > 0) {
      recommendations.push(`${summary.timeout} 個頻道響應超時，可能是服務器負載過高或網路問題`);
    }

    if (summary.slow > 0) {
      recommendations.push(`${summary.slow} 個頻道響應緩慢，可能影響播放體驗`);
    }

    if (summary.error > 0) {
      recommendations.push(`${summary.error} 個頻道無法存取，建議聯繫服務提供商`);
    }

    const healthPercentage = (summary.healthy / summary.total) * 100;
    if (healthPercentage < 70) {
      recommendations.push('整體健康度較低，建議更換播放清單或聯繫服務提供商');
    }

    // 檢查是否所有頻道都使用同一個域名
    const domains = new Set(results.map(r => new URL(r.channel.url).hostname));
    if (domains.size === 1) {
      recommendations.push('所有頻道使用同一個服務器，存在單點故障風險');
    }

    return {
      summary,
      recommendations,
      problematicChannels
    };
  }

  /**
   * 檢查是否為 HLS 內容
   */
  private static isHLSContent(contentType: string | null): boolean {
    if (!contentType) return false;
    
    return contentType.includes('application/vnd.apple.mpegurl') ||
           contentType.includes('application/x-mpegurl') ||
           contentType.includes('video/mp2t');
  }

  /**
   * 獲取頻道的詳細資訊（需要實際下載內容）
   */
  static async getChannelDetails(channel: M3UChannel): Promise<{
    segmentCount?: number;
    resolution?: string;
    bitrate?: number;
    duration?: number;
  }> {
    try {
      const response = await fetch(channel.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/vnd.apple.mpegurl,application/x-mpegurl,*/*'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const content = await response.text();
      
      // 簡單的 M3U8 解析
      const lines = content.split('\n');
      let segmentCount = 0;
      let resolution = '';
      let bitrate = 0;

      for (const line of lines) {
        if (line.startsWith('#EXTINF:')) {
          segmentCount++;
        } else if (line.includes('RESOLUTION=')) {
          const match = line.match(/RESOLUTION=(\d+x\d+)/);
          if (match) {
            resolution = match[1];
          }
        } else if (line.includes('BANDWIDTH=')) {
          const match = line.match(/BANDWIDTH=(\d+)/);
          if (match) {
            bitrate = parseInt(match[1]);
          }
        }
      }

      return {
        segmentCount: segmentCount > 0 ? segmentCount : undefined,
        resolution: resolution || undefined,
        bitrate: bitrate > 0 ? bitrate : undefined
      };

    } catch (error) {
      console.warn('無法獲取頻道詳細資訊:', error);
      return {};
    }
  }
}
