// CORS 代理工具
export class CorsProxy {
  private static proxies = [
    'https://api.allorigins.win/raw?url=',
    'https://cors-anywhere.herokuapp.com/',
    'https://thingproxy.freeboard.io/fetch/',
    'https://api.codetabs.com/v1/proxy?quest=',
  ];

  private static currentProxyIndex = 0;

  /**
   * 獲取當前代理 URL
   */
  private static getCurrentProxy(): string {
    return this.proxies[this.currentProxyIndex];
  }

  /**
   * 切換到下一個代理
   */
  private static switchToNextProxy(): void {
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
    console.log(`🔄 切換到代理: ${this.getCurrentProxy()}`);
  }

  /**
   * 使用代理獲取資源
   */
  static async fetchWithProxy(url: string, options: RequestInit = {}): Promise<Response> {
    const maxRetries = this.proxies.length;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const proxy = this.getCurrentProxy();
        const proxyUrl = `${proxy}${encodeURIComponent(url)}`;
        
        console.log(`🌐 嘗試使用代理 ${attempt + 1}/${maxRetries}: ${proxy}`);
        console.log(`📡 請求 URL: ${proxyUrl}`);

        const response = await fetch(proxyUrl, {
          ...options,
          headers: {
            'Accept': 'text/plain, application/x-mpegURL, */*',
            'User-Agent': 'TVBOX/1.0',
            ...options.headers,
          },
        });

        if (response.ok) {
          console.log(`✅ 代理請求成功: ${proxy}`);
          return response;
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.warn(`❌ 代理失敗 ${attempt + 1}/${maxRetries}:`, error);
        lastError = error as Error;
        
        if (attempt < maxRetries - 1) {
          this.switchToNextProxy();
          // 等待一下再重試
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // 所有代理都失敗，嘗試直接請求
    console.log('🔄 所有代理都失敗，嘗試直接請求...');
    try {
      const response = await fetch(url, options);
      if (response.ok) {
        console.log('✅ 直接請求成功');
        return response;
      }
    } catch (error) {
      console.warn('❌ 直接請求也失敗:', error);
    }

    throw new Error(`所有請求方式都失敗。最後錯誤: ${lastError?.message}`);
  }

  /**
   * 檢查 URL 是否需要代理
   */
  static needsProxy(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const currentOrigin = window.location.origin;
      
      // 如果是同源請求，不需要代理
      if (urlObj.origin === currentOrigin) {
        return false;
      }

      // 檢查是否是已知的需要代理的域名
      const needsProxyDomains = [
        'files.catbox.moe',
        'raw.githubusercontent.com',
        'gist.githubusercontent.com',
        'pastebin.com',
      ];

      return needsProxyDomains.some(domain => urlObj.hostname.includes(domain));
    } catch {
      return false;
    }
  }

  /**
   * 智能獲取資源（自動判斷是否需要代理）
   */
  static async smartFetch(url: string, options: RequestInit = {}): Promise<Response> {
    console.log(`🔍 智能請求: ${url}`);
    
    if (this.needsProxy(url)) {
      console.log('🌐 檢測到需要代理的 URL，使用代理請求');
      return this.fetchWithProxy(url, options);
    } else {
      console.log('📡 直接請求');
      return fetch(url, options);
    }
  }
}

// 導出便捷函數
export const fetchWithCorsProxy = CorsProxy.fetchWithProxy.bind(CorsProxy);
export const smartFetch = CorsProxy.smartFetch.bind(CorsProxy);
