import Hls from 'hls.js';

// 頻道特定優化配置
export interface ChannelOptimization {
  name: string;
  patterns: string[];
  hlsConfig: Partial<Hls.Config>;
  description: string;
}

// 預定義的頻道優化配置
export const channelOptimizations: ChannelOptimization[] = [
  {
    name: '中視新聞台',
    patterns: [
      'ctv',
      '中視',
      'ctvnews',
      '4gtv-4gtv074',
      'koyeb'
    ],
    hlsConfig: {
      // 針對 Koyeb 代理的優化
      maxBufferLength: 60,           // 增加緩衝長度
      maxMaxBufferLength: 120,       // 最大緩衝長度
      maxBufferSize: 60 * 1000 * 1000, // 60MB
      maxBufferHole: 0.5,            // 允許更大的緩衝洞
      
      // 網路優化
      manifestLoadingTimeOut: 20000,  // 20秒超時
      manifestLoadingMaxRetry: 5,     // 增加重試次數
      manifestLoadingRetryDelay: 2000, // 重試延遲
      
      // 片段載入優化
      fragLoadingTimeOut: 30000,      // 30秒超時
      fragLoadingMaxRetry: 6,         // 增加重試次數
      fragLoadingRetryDelay: 1500,    // 重試延遲
      
      // 品質切換優化
      abrEwmaFastLive: 5.0,          // 快速適應
      abrEwmaSlowLive: 9.0,          // 慢速適應
      abrMaxWithRealBitrate: true,    // 使用真實比特率
      abrBandWidthFactor: 0.8,       // 保守的頻寬因子
      abrBandWidthUpFactor: 0.7,     // 更保守的上調因子
      
      // 錯誤恢復
      enableWorker: true,             // 啟用 Web Worker
      enableSoftwareAES: true,        // 軟體 AES 解密
      startFragPrefetch: true,        // 預取片段
      testBandwidth: false,           // 禁用頻寬測試避免額外負載
      
      // 直播優化
      liveSyncDurationCount: 3,       // 同步持續時間
      liveMaxLatencyDurationCount: 10, // 最大延遲
      liveDurationInfinity: true,     // 無限直播持續時間
      
      // 緩衝策略
      backBufferLength: 30,           // 保留30秒後向緩衝
      frontBufferFlushThreshold: 2 * 60, // 2分鐘前向緩衝閾值
    },
    description: '針對中視新聞台和 Koyeb 代理服務器的優化配置，增強緩衝和錯誤恢復能力'
  },
  {
    name: 'Koyeb 代理服務',
    patterns: [
      'koyeb.app',
      'breezy-audrie',
      'zspace'
    ],
    hlsConfig: {
      // 針對代理服務器的通用優化
      maxBufferLength: 45,
      maxMaxBufferLength: 90,
      maxBufferSize: 45 * 1000 * 1000,
      
      manifestLoadingTimeOut: 15000,
      manifestLoadingMaxRetry: 4,
      manifestLoadingRetryDelay: 1500,
      
      fragLoadingTimeOut: 25000,
      fragLoadingMaxRetry: 5,
      fragLoadingRetryDelay: 1200,
      
      abrBandWidthFactor: 0.75,
      abrBandWidthUpFactor: 0.65,
      
      enableWorker: true,
      startFragPrefetch: true,
      testBandwidth: false,
      
      liveSyncDurationCount: 3,
      liveMaxLatencyDurationCount: 8,
    },
    description: '針對 Koyeb 等代理服務的通用優化配置'
  },
  {
    name: '高延遲網路',
    patterns: [
      'slow',
      'high-latency'
    ],
    hlsConfig: {
      maxBufferLength: 90,
      maxMaxBufferLength: 180,
      maxBufferSize: 90 * 1000 * 1000,
      
      manifestLoadingTimeOut: 30000,
      manifestLoadingMaxRetry: 6,
      manifestLoadingRetryDelay: 3000,
      
      fragLoadingTimeOut: 45000,
      fragLoadingMaxRetry: 8,
      fragLoadingRetryDelay: 2000,
      
      abrBandWidthFactor: 0.6,
      abrBandWidthUpFactor: 0.5,
      
      liveSyncDurationCount: 5,
      liveMaxLatencyDurationCount: 15,
      
      backBufferLength: 60,
    },
    description: '針對高延遲或不穩定網路環境的優化配置'
  }
];

// 根據頻道 URL 和名稱匹配最佳優化配置
export function getOptimalConfig(channelName: string, channelUrl: string): Partial<Hls.Config> {
  const searchText = `${channelName} ${channelUrl}`.toLowerCase();
  
  // 尋找匹配的優化配置
  for (const optimization of channelOptimizations) {
    const matches = optimization.patterns.some(pattern => 
      searchText.includes(pattern.toLowerCase())
    );
    
    if (matches) {
      console.log(`🎯 應用優化配置: ${optimization.name}`);
      console.log(`📝 描述: ${optimization.description}`);
      return optimization.hlsConfig;
    }
  }
  
  // 預設配置
  console.log('📺 使用預設配置');
  return {
    maxBufferLength: 30,
    maxMaxBufferLength: 60,
    maxBufferSize: 30 * 1000 * 1000,
    
    manifestLoadingTimeOut: 10000,
    manifestLoadingMaxRetry: 3,
    manifestLoadingRetryDelay: 1000,
    
    fragLoadingTimeOut: 20000,
    fragLoadingMaxRetry: 4,
    fragLoadingRetryDelay: 1000,
    
    abrBandWidthFactor: 0.95,
    abrBandWidthUpFactor: 0.7,
    
    enableWorker: true,
    startFragPrefetch: true,
    testBandwidth: true,
    
    liveSyncDurationCount: 3,
    liveMaxLatencyDurationCount: 6,
  };
}

// 動態調整配置（基於網路狀況）
export function adjustConfigForNetworkConditions(
  baseConfig: Partial<Hls.Config>,
  networkCondition: 'good' | 'fair' | 'poor'
): Partial<Hls.Config> {
  const adjustedConfig = { ...baseConfig };
  
  switch (networkCondition) {
    case 'poor':
      // 網路狀況差時的調整
      adjustedConfig.maxBufferLength = (adjustedConfig.maxBufferLength || 30) * 1.5;
      adjustedConfig.maxMaxBufferLength = (adjustedConfig.maxMaxBufferLength || 60) * 1.5;
      adjustedConfig.abrBandWidthFactor = 0.6;
      adjustedConfig.abrBandWidthUpFactor = 0.4;
      adjustedConfig.fragLoadingMaxRetry = (adjustedConfig.fragLoadingMaxRetry || 4) + 2;
      adjustedConfig.manifestLoadingMaxRetry = (adjustedConfig.manifestLoadingMaxRetry || 3) + 2;
      break;
      
    case 'fair':
      // 網路狀況一般時的調整
      adjustedConfig.maxBufferLength = (adjustedConfig.maxBufferLength || 30) * 1.2;
      adjustedConfig.abrBandWidthFactor = 0.75;
      adjustedConfig.abrBandWidthUpFactor = 0.6;
      break;
      
    case 'good':
    default:
      // 網路狀況良好時保持原配置
      break;
  }
  
  return adjustedConfig;
}

// 檢測網路狀況
export async function detectNetworkCondition(testUrl?: string): Promise<'good' | 'fair' | 'poor'> {
  try {
    const startTime = Date.now();
    const response = await fetch(testUrl || 'https://www.google.com/favicon.ico', {
      method: 'HEAD',
      cache: 'no-cache'
    });
    const latency = Date.now() - startTime;
    
    if (!response.ok) {
      return 'poor';
    }
    
    if (latency < 200) {
      return 'good';
    } else if (latency < 500) {
      return 'fair';
    } else {
      return 'poor';
    }
  } catch (error) {
    console.warn('網路狀況檢測失敗:', error);
    return 'fair'; // 預設為一般狀況
  }
}

// 獲取完整的優化配置
export async function getFullOptimizedConfig(
  channelName: string,
  channelUrl: string,
  testNetworkCondition: boolean = true
): Promise<Partial<Hls.Config>> {
  let baseConfig = getOptimalConfig(channelName, channelUrl);
  
  if (testNetworkCondition) {
    try {
      const networkCondition = await detectNetworkCondition();
      console.log(`🌐 網路狀況: ${networkCondition}`);
      baseConfig = adjustConfigForNetworkConditions(baseConfig, networkCondition);
    } catch (error) {
      console.warn('無法檢測網路狀況，使用基礎配置:', error);
    }
  }
  
  return baseConfig;
}
