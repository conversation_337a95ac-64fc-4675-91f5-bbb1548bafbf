import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { ChannelTester, type ChannelTestResult } from '../../utils/channelTester';
import type { M3UChannel } from '../../types';

interface HealthCheckResultsProps {
  results: ChannelTestResult[];
  onClose: () => void;
  onRetryChannel: (channel: M3UChannel) => void;
}

export const HealthCheckResults: React.FC<HealthCheckResultsProps> = ({
  results,
  onClose,
  onRetryChannel
}) => {
  const [filter, setFilter] = useState<'all' | 'healthy' | 'slow' | 'error' | 'timeout' | 'cors' | 'auth'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'status' | 'responseTime'>('status');

  const getStatusColor = (status: ChannelTestResult['status']) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800';
      case 'slow': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'timeout': return 'bg-orange-100 text-orange-800';
      case 'cors': return 'bg-purple-100 text-purple-800';
      case 'auth': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: ChannelTestResult['status']) => {
    switch (status) {
      case 'healthy': return '健康';
      case 'slow': return '緩慢';
      case 'error': return '錯誤';
      case 'timeout': return '超時';
      case 'cors': return 'CORS';
      case 'auth': return '認證';
      default: return '未知';
    }
  };

  const filteredResults = results.filter(result => {
    if (filter === 'all') return true;
    return result.status === filter;
  });

  const sortedResults = [...filteredResults].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.channel.name.localeCompare(b.channel.name);
      case 'status':
        const statusOrder = { 'error': 0, 'timeout': 1, 'cors': 2, 'auth': 3, 'slow': 4, 'healthy': 5 };
        return statusOrder[a.status] - statusOrder[b.status];
      case 'responseTime':
        return b.responseTime - a.responseTime;
      default:
        return 0;
    }
  });

  const stats = {
    total: results.length,
    healthy: results.filter(r => r.status === 'healthy').length,
    slow: results.filter(r => r.status === 'slow').length,
    error: results.filter(r => r.status === 'error').length,
    timeout: results.filter(r => r.status === 'timeout').length,
    cors: results.filter(r => r.status === 'cors').length,
    auth: results.filter(r => r.status === 'auth').length,
  };

  const healthyPercentage = Math.round((stats.healthy / stats.total) * 100);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 標題欄 */}
        <div className="bg-gray-50 px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">頻道健康檢查結果</h2>
            <Button variant="ghost" onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        {/* 統計摘要 */}
        <div className="px-6 py-4 border-b bg-gray-50">
          <div className="grid grid-cols-3 md:grid-cols-7 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
              <div className="text-sm text-gray-600">總計</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{stats.healthy}</div>
              <div className="text-sm text-gray-600">健康</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">{stats.slow}</div>
              <div className="text-sm text-gray-600">緩慢</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600">{stats.timeout}</div>
              <div className="text-sm text-gray-600">超時</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">{stats.cors}</div>
              <div className="text-sm text-gray-600">CORS</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{stats.auth}</div>
              <div className="text-sm text-gray-600">認證</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{stats.error}</div>
              <div className="text-sm text-gray-600">錯誤</div>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm mb-2">
              <span>整體健康度</span>
              <span className="font-semibold">{healthyPercentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${healthyPercentage}%` }}
              />
            </div>
          </div>
        </div>

        {/* 篩選和排序 */}
        <div className="px-6 py-3 border-b bg-white">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">篩選:</label>
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="border rounded px-2 py-1 text-sm"
              >
                <option value="all">全部 ({stats.total})</option>
                <option value="healthy">健康 ({stats.healthy})</option>
                <option value="slow">緩慢 ({stats.slow})</option>
                <option value="timeout">超時 ({stats.timeout})</option>
                <option value="cors">CORS ({stats.cors})</option>
                <option value="auth">認證 ({stats.auth})</option>
                <option value="error">錯誤 ({stats.error})</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">排序:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="border rounded px-2 py-1 text-sm"
              >
                <option value="status">狀態</option>
                <option value="name">名稱</option>
                <option value="responseTime">響應時間</option>
              </select>
            </div>
          </div>
        </div>

        {/* 結果列表 */}
        <div className="overflow-y-auto max-h-96">
          <div className="divide-y">
            {sortedResults.map((result, index) => (
              <div key={index} className="px-6 py-3 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}
                      >
                        {getStatusText(result.status)}
                      </span>
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {result.channel.name}
                      </h3>
                      {result.channel.group && (
                        <span className="text-xs text-gray-500">
                          {result.channel.group}
                        </span>
                      )}
                    </div>
                    
                    <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                      <span>響應時間: {result.responseTime}ms</span>
                      {result.error && (
                        <span className="text-red-600">錯誤: {result.error}</span>
                      )}
                      {result.details?.manifestSize && (
                        <span>大小: {Math.round(result.details.manifestSize / 1024)}KB</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {result.status !== 'healthy' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onRetryChannel(result.channel)}
                        className="text-xs"
                      >
                        重試
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 建議 */}
        <div className="px-6 py-4 bg-blue-50 border-t">
          <h4 className="font-medium text-blue-900 mb-2">優化建議:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            {stats.error > 0 && (
              <li>• {stats.error} 個頻道無法連接，可能是服務器問題或 CORS 限制</li>
            )}
            {stats.timeout > 0 && (
              <li>• {stats.timeout} 個頻道響應緩慢，建議檢查網路連接</li>
            )}
            {stats.slow > 0 && (
              <li>• {stats.slow} 個頻道載入較慢，可能影響播放體驗</li>
            )}
            {healthyPercentage < 70 && (
              <li>• 整體健康度較低，建議聯繫服務提供商或更換播放清單</li>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};
