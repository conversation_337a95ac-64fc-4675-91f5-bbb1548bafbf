import React, { useState } from 'react';
import { Button } from '../ui/Button';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { ChannelTester, type ChannelTestResult } from '../../utils/channelTester';
import type { M3UChannel } from '../../types';

interface ChannelHealthCheckerProps {
  channels: M3UChannel[];
  onResults: (results: ChannelTestResult[]) => void;
}

export const ChannelHealthChecker: React.FC<ChannelHealthCheckerProps> = ({
  channels,
  onResults
}) => {
  const [isChecking, setIsChecking] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentChannel, setCurrentChannel] = useState<string>('');

  const runHealthCheck = async () => {
    setIsChecking(true);
    setProgress(0);
    setCurrentChannel('');

    const results = await ChannelTester.testChannels(
      channels,
      (current, total, channel) => {
        setCurrentChannel(channel.name);
        setProgress((current / total) * 100);
      }
    );

    setIsChecking(false);
    setCurrentChannel('');
    onResults(results);
  };

  const getStatusColor = (status: ChannelTestResult['status']) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'slow': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      case 'timeout': return 'text-orange-500';
      case 'cors': return 'text-purple-500';
      case 'auth': return 'text-blue-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = (status: ChannelTestResult['status']) => {
    switch (status) {
      case 'healthy': return '健康';
      case 'slow': return '緩慢';
      case 'error': return '錯誤';
      case 'timeout': return '超時';
      case 'cors': return 'CORS';
      case 'auth': return '認證';
      default: return '未知';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">頻道健康檢查</h3>
        <Button
          onClick={runHealthCheck}
          disabled={isChecking}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isChecking ? '檢查中...' : '開始檢查'}
        </Button>
      </div>

      {isChecking && (
        <div className="mb-4">
          <div className="flex items-center space-x-3 mb-2">
            <LoadingSpinner size="sm" />
            <span className="text-sm text-gray-600">
              正在檢查: {currentChannel}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {Math.round(progress)}% 完成
          </div>
        </div>
      )}

      <div className="text-sm text-gray-600">
        <p className="mb-2">
          <strong>檢查項目：</strong>
        </p>
        <ul className="list-disc list-inside space-y-1 text-xs">
          <li>連接響應時間</li>
          <li>HTTP 狀態碼</li>
          <li>服務器可用性</li>
          <li>CORS 政策</li>
        </ul>
        
        <p className="mt-3 mb-2">
          <strong>狀態說明：</strong>
        </p>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 bg-green-500 rounded-full"></span>
            <span>健康 (&lt;2秒)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 bg-yellow-500 rounded-full"></span>
            <span>緩慢 (2-5秒)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 bg-orange-500 rounded-full"></span>
            <span>超時 (&gt;5秒)</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="w-3 h-3 bg-red-500 rounded-full"></span>
            <span>錯誤</span>
          </div>
        </div>
      </div>
    </div>
  );
};
