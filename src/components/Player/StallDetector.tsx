import React, { useEffect, useRef, useState } from 'react';
import { usePlayerStore } from '../../stores/playerStore';

interface StallEvent {
  timestamp: number;
  duration: number;
  bufferLevel: number;
  currentTime: number;
  readyState: number;
}

interface StallDetectorProps {
  onStallDetected?: (event: StallEvent) => void;
  onStallResolved?: (event: StallEvent) => void;
  autoRecover?: boolean;
}

export const StallDetector: React.FC<StallDetectorProps> = ({
  onStallDetected,
  onStallResolved,
  autoRecover = true
}) => {
  const { hlsInstance, isPlaying } = usePlayerStore();
  const [stallEvents, setStallEvents] = useState<StallEvent[]>([]);
  const [isStalled, setIsStalled] = useState(false);
  const [stallStartTime, setStallStartTime] = useState<number | null>(null);
  
  const lastProgressTimeRef = useRef<number>(0);
  const stallCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const recoveryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const stallCountRef = useRef(0);

  // 檢測卡頓的函數
  const checkForStall = () => {
    const video = document.querySelector('video') as HTMLVideoElement;
    if (!video || !isPlaying) return;

    const currentTime = video.currentTime;
    const readyState = video.readyState;
    const buffered = video.buffered;
    
    // 計算緩衝級別
    let bufferLevel = 0;
    if (buffered.length > 0) {
      const bufferedEnd = buffered.end(buffered.length - 1);
      bufferLevel = bufferedEnd - currentTime;
    }

    // 檢測是否卡頓
    const isCurrentlyStalled = (
      !video.paused && 
      !video.ended && 
      (readyState < 3 || currentTime === lastProgressTimeRef.current) &&
      bufferLevel < 0.5
    );

    if (isCurrentlyStalled && !isStalled) {
      // 開始卡頓
      const stallStart = Date.now();
      setIsStalled(true);
      setStallStartTime(stallStart);
      stallCountRef.current++;

      const stallEvent: StallEvent = {
        timestamp: stallStart,
        duration: 0,
        bufferLevel,
        currentTime,
        readyState
      };

      console.warn('🚫 檢測到卡頓:', stallEvent);
      onStallDetected?.(stallEvent);

      // 自動恢復機制
      if (autoRecover) {
        attemptRecovery(stallEvent);
      }

    } else if (!isCurrentlyStalled && isStalled && stallStartTime) {
      // 卡頓結束
      const stallEnd = Date.now();
      const duration = stallEnd - stallStartTime;
      
      const resolvedEvent: StallEvent = {
        timestamp: stallEnd,
        duration,
        bufferLevel,
        currentTime,
        readyState
      };

      console.log('✅ 卡頓已解決:', resolvedEvent);
      setIsStalled(false);
      setStallStartTime(null);
      
      // 記錄卡頓事件
      setStallEvents(prev => [resolvedEvent, ...prev.slice(0, 9)]);
      onStallResolved?.(resolvedEvent);

      // 清除恢復超時
      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current);
        recoveryTimeoutRef.current = null;
      }
    }

    lastProgressTimeRef.current = currentTime;
  };

  // 自動恢復機制
  const attemptRecovery = (stallEvent: StallEvent) => {
    console.log('🔧 嘗試自動恢復卡頓...');

    const video = document.querySelector('video') as HTMLVideoElement;
    if (!video) return;

    // 恢復策略 1: 降低品質
    if (hlsInstance && hlsInstance.currentLevel > 0) {
      console.log('📉 降低品質以改善播放穩定性');
      hlsInstance.currentLevel = Math.max(0, hlsInstance.currentLevel - 1);
    }

    // 恢復策略 2: 微調播放位置
    recoveryTimeoutRef.current = setTimeout(() => {
      if (video.readyState < 3 && !video.paused) {
        console.log('⏭️ 微調播放位置');
        try {
          // 跳過一小段時間
          video.currentTime += 0.1;
        } catch (error) {
          console.warn('無法微調播放位置:', error);
        }
      }
    }, 2000);

    // 恢復策略 3: 強制重新載入片段
    setTimeout(() => {
      if (isStalled && hlsInstance) {
        console.log('🔄 強制重新載入當前片段');
        try {
          // 觸發緩衝區刷新
          hlsInstance.trigger('hlsBufferFlushed');
        } catch (error) {
          console.warn('無法刷新緩衝區:', error);
        }
      }
    }, 5000);

    // 恢復策略 4: 完全重新初始化（最後手段）
    setTimeout(() => {
      if (isStalled) {
        console.log('🆘 執行完全重新初始化');
        // 這裡可以觸發播放器重新初始化
        window.location.reload();
      }
    }, 15000);
  };

  // 獲取卡頓統計
  const getStallStats = () => {
    const totalStalls = stallEvents.length;
    const totalStallTime = stallEvents.reduce((sum, event) => sum + event.duration, 0);
    const avgStallDuration = totalStalls > 0 ? totalStallTime / totalStalls : 0;
    
    return {
      totalStalls,
      totalStallTime,
      avgStallDuration,
      currentStallCount: stallCountRef.current
    };
  };

  // 啟動卡頓檢測
  useEffect(() => {
    if (isPlaying) {
      stallCheckIntervalRef.current = setInterval(checkForStall, 1000);
    } else {
      if (stallCheckIntervalRef.current) {
        clearInterval(stallCheckIntervalRef.current);
        stallCheckIntervalRef.current = null;
      }
    }

    return () => {
      if (stallCheckIntervalRef.current) {
        clearInterval(stallCheckIntervalRef.current);
      }
      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current);
      }
    };
  }, [isPlaying]);

  // 重置統計
  const resetStats = () => {
    setStallEvents([]);
    stallCountRef.current = 0;
    setIsStalled(false);
    setStallStartTime(null);
  };

  const stats = getStallStats();

  return (
    <div className="fixed bottom-32 right-4 bg-gray-900 text-white p-3 rounded-lg shadow-xl text-xs max-w-xs z-40">
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-medium text-yellow-400">卡頓監控</h4>
        <button
          onClick={resetStats}
          className="text-gray-400 hover:text-white text-xs"
        >
          重置
        </button>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>狀態:</span>
          <span className={isStalled ? 'text-red-400' : 'text-green-400'}>
            {isStalled ? '卡頓中' : '正常'}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>卡頓次數:</span>
          <span className="text-red-400">{stats.currentStallCount}</span>
        </div>
        
        <div className="flex justify-between">
          <span>總卡頓時間:</span>
          <span className="text-yellow-400">
            {(stats.totalStallTime / 1000).toFixed(1)}s
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>平均卡頓:</span>
          <span className="text-blue-400">
            {(stats.avgStallDuration / 1000).toFixed(1)}s
          </span>
        </div>
        
        {isStalled && stallStartTime && (
          <div className="flex justify-between">
            <span>當前卡頓:</span>
            <span className="text-red-400">
              {((Date.now() - stallStartTime) / 1000).toFixed(1)}s
            </span>
          </div>
        )}
      </div>

      {stallEvents.length > 0 && (
        <div className="mt-3 pt-2 border-t border-gray-700">
          <div className="text-gray-400 mb-1">最近卡頓:</div>
          <div className="space-y-1 max-h-20 overflow-y-auto">
            {stallEvents.slice(0, 3).map((event, index) => (
              <div key={index} className="text-xs">
                <span className="text-gray-500">
                  {new Date(event.timestamp).toLocaleTimeString()}
                </span>
                <span className="ml-2 text-red-400">
                  {(event.duration / 1000).toFixed(1)}s
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
