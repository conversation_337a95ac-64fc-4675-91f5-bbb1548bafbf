import React, { useState, useEffect } from 'react';
import { usePlayerStore } from '../../stores/playerStore';

interface DiagnosticsData {
  bufferHealth: string;
  currentQuality: string;
  networkStatus: string;
  errorCount: number;
  lastError: string | null;
  playbackRate: number;
  droppedFrames: number;
  totalFrames: number;
}

interface PlayerDiagnosticsProps {
  isVisible: boolean;
  onToggle: () => void;
}

export const PlayerDiagnostics: React.FC<PlayerDiagnosticsProps> = ({
  isVisible,
  onToggle
}) => {
  const { hlsInstance, currentChannel, error } = usePlayerStore();
  const [diagnostics, setDiagnostics] = useState<DiagnosticsData>({
    bufferHealth: '未知',
    currentQuality: '未知',
    networkStatus: '未知',
    errorCount: 0,
    lastError: null,
    playbackRate: 1.0,
    droppedFrames: 0,
    totalFrames: 0
  });

  useEffect(() => {
    if (!isVisible) return;

    const updateDiagnostics = () => {
      const videoElement = document.querySelector('video') as HTMLVideoElement;
      
      if (!videoElement) return;

      const newDiagnostics: DiagnosticsData = {
        bufferHealth: getBufferHealth(videoElement),
        currentQuality: getCurrentQuality(),
        networkStatus: getNetworkStatus(),
        errorCount: diagnostics.errorCount + (error ? 1 : 0),
        lastError: error,
        playbackRate: videoElement.playbackRate,
        droppedFrames: getDroppedFrames(videoElement),
        totalFrames: getTotalFrames(videoElement)
      };

      setDiagnostics(newDiagnostics);
    };

    const interval = setInterval(updateDiagnostics, 1000);
    updateDiagnostics(); // 立即更新一次

    return () => clearInterval(interval);
  }, [isVisible, hlsInstance, error, diagnostics.errorCount]);

  const getBufferHealth = (video: HTMLVideoElement): string => {
    if (video.buffered.length === 0) return '無緩衝';
    
    const currentTime = video.currentTime;
    const bufferedEnd = video.buffered.end(video.buffered.length - 1);
    const bufferedAhead = bufferedEnd - currentTime;
    
    if (bufferedAhead > 10) return `健康 (${bufferedAhead.toFixed(1)}s)`;
    if (bufferedAhead > 5) return `良好 (${bufferedAhead.toFixed(1)}s)`;
    if (bufferedAhead > 2) return `警告 (${bufferedAhead.toFixed(1)}s)`;
    return `危險 (${bufferedAhead.toFixed(1)}s)`;
  };

  const getCurrentQuality = (): string => {
    if (!hlsInstance || !hlsInstance.levels) return '未知';
    
    const currentLevel = hlsInstance.currentLevel;
    if (currentLevel === -1) return '自動';
    
    const level = hlsInstance.levels[currentLevel];
    if (!level) return '未知';
    
    return `${level.height || '?'}p (${Math.round((level.bitrate || 0) / 1000)}kbps)`;
  };

  const getNetworkStatus = (): string => {
    if (!hlsInstance) return '未知';
    
    // 簡單的網路狀態檢測
    const stats = hlsInstance.stats;
    if (stats && stats.abr) {
      const bandwidth = stats.abr.bw || 0;
      if (bandwidth > 5000000) return '優秀';
      if (bandwidth > 2000000) return '良好';
      if (bandwidth > 1000000) return '一般';
      if (bandwidth > 500000) return '較差';
      return '很差';
    }
    
    return '檢測中';
  };

  const getDroppedFrames = (video: HTMLVideoElement): number => {
    // @ts-ignore - 這是實驗性 API
    const quality = video.getVideoPlaybackQuality?.();
    return quality?.droppedVideoFrames || 0;
  };

  const getTotalFrames = (video: HTMLVideoElement): number => {
    // @ts-ignore - 這是實驗性 API
    const quality = video.getVideoPlaybackQuality?.();
    return quality?.totalVideoFrames || 0;
  };

  const getHealthColor = (health: string): string => {
    if (health.includes('健康') || health.includes('優秀')) return 'text-green-400';
    if (health.includes('良好')) return 'text-yellow-400';
    if (health.includes('警告') || health.includes('一般')) return 'text-orange-400';
    return 'text-red-400';
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm hover:bg-gray-700 transition-colors z-50"
      >
        診斷
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold">播放診斷</h3>
        <button
          onClick={onToggle}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>頻道:</span>
          <span className="text-blue-400">{currentChannel?.name || '未選擇'}</span>
        </div>
        
        <div className="flex justify-between">
          <span>緩衝狀態:</span>
          <span className={getHealthColor(diagnostics.bufferHealth)}>
            {diagnostics.bufferHealth}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>當前品質:</span>
          <span className="text-purple-400">{diagnostics.currentQuality}</span>
        </div>
        
        <div className="flex justify-between">
          <span>網路狀態:</span>
          <span className={getHealthColor(diagnostics.networkStatus)}>
            {diagnostics.networkStatus}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>播放速率:</span>
          <span className="text-cyan-400">{diagnostics.playbackRate.toFixed(2)}x</span>
        </div>
        
        {diagnostics.totalFrames > 0 && (
          <div className="flex justify-between">
            <span>丟幀率:</span>
            <span className={diagnostics.droppedFrames > 0 ? 'text-red-400' : 'text-green-400'}>
              {((diagnostics.droppedFrames / diagnostics.totalFrames) * 100).toFixed(2)}%
            </span>
          </div>
        )}
        
        <div className="flex justify-between">
          <span>錯誤次數:</span>
          <span className={diagnostics.errorCount > 0 ? 'text-red-400' : 'text-green-400'}>
            {diagnostics.errorCount}
          </span>
        </div>
        
        {diagnostics.lastError && (
          <div className="mt-2 p-2 bg-red-900 rounded text-xs">
            <div className="font-semibold">最後錯誤:</div>
            <div className="text-red-300">{diagnostics.lastError}</div>
          </div>
        )}
      </div>
      
      <div className="mt-3 pt-3 border-t border-gray-700">
        <div className="text-xs text-gray-400">
          實時監控 • 每秒更新
        </div>
      </div>
    </div>
  );
};
