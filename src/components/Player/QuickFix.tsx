import React, { useState } from 'react';
import { usePlayerStore } from '../../stores/playerStore';
import { Button } from '../ui/Button';

interface QuickFixProps {
  channelName?: string;
  channelUrl?: string;
}

export const QuickFix: React.FC<QuickFixProps> = ({ channelName, channelUrl }) => {
  const { hlsInstance } = usePlayerStore();
  const [isVisible, setIsVisible] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const [lastFixTime, setLastFixTime] = useState<number | null>(null);

  // 檢查是否為中視新聞台或類似頻道
  const isTargetChannel = channelName?.includes('中視') || 
                         channelUrl?.includes('koyeb') || 
                         channelUrl?.includes('4gtv-4gtv074');

  // 快速修復函數
  const quickFixes = {
    // 修復 1: 重新載入當前片段
    reloadSegment: async () => {
      if (!hlsInstance) return false;
      
      try {
        console.log('🔄 重新載入當前片段...');
        hlsInstance.trigger('hlsBufferFlushed');
        await new Promise(resolve => setTimeout(resolve, 1000));
        return true;
      } catch (error) {
        console.error('重新載入片段失敗:', error);
        return false;
      }
    },

    // 修復 2: 降低品質
    lowerQuality: async () => {
      if (!hlsInstance || hlsInstance.levels.length <= 1) return false;
      
      try {
        const currentLevel = hlsInstance.currentLevel;
        const newLevel = Math.max(0, currentLevel - 1);
        
        console.log(`📉 降低品質: Level ${currentLevel} -> Level ${newLevel}`);
        hlsInstance.currentLevel = newLevel;
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        return true;
      } catch (error) {
        console.error('降低品質失敗:', error);
        return false;
      }
    },

    // 修復 3: 清理緩衝區
    clearBuffer: async () => {
      const video = document.querySelector('video') as HTMLVideoElement;
      if (!video || !hlsInstance) return false;
      
      try {
        console.log('🧹 清理緩衝區...');
        
        // 暫停播放
        const wasPlaying = !video.paused;
        video.pause();
        
        // 清理緩衝
        hlsInstance.trigger('hlsBufferFlushed');
        
        // 等待一下再恢復播放
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        if (wasPlaying) {
          await video.play();
        }
        
        return true;
      } catch (error) {
        console.error('清理緩衝區失敗:', error);
        return false;
      }
    },

    // 修復 4: 跳過卡頓點
    skipStall: async () => {
      const video = document.querySelector('video') as HTMLVideoElement;
      if (!video) return false;
      
      try {
        console.log('⏭️ 跳過卡頓點...');
        const currentTime = video.currentTime;
        video.currentTime = currentTime + 2; // 跳過 2 秒
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        return true;
      } catch (error) {
        console.error('跳過卡頓點失敗:', error);
        return false;
      }
    },

    // 修復 5: 重新連接
    reconnect: async () => {
      if (!hlsInstance || !channelUrl) return false;
      
      try {
        console.log('🔄 重新連接...');
        
        // 銷毀當前實例
        hlsInstance.destroy();
        
        // 等待一下讓頁面重新初始化播放器
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 重新載入頁面作為最後手段
        window.location.reload();
        
        return true;
      } catch (error) {
        console.error('重新連接失敗:', error);
        return false;
      }
    }
  };

  // 執行自動修復序列
  const runAutoFix = async () => {
    if (isFixing) return;
    
    setIsFixing(true);
    setLastFixTime(Date.now());
    
    console.log('🚀 開始自動修復序列...');
    
    const fixes = [
      { name: '重新載入片段', fn: quickFixes.reloadSegment },
      { name: '清理緩衝區', fn: quickFixes.clearBuffer },
      { name: '降低品質', fn: quickFixes.lowerQuality },
      { name: '跳過卡頓點', fn: quickFixes.skipStall }
    ];
    
    for (const fix of fixes) {
      console.log(`🔧 嘗試: ${fix.name}`);
      
      try {
        const success = await fix.fn();
        if (success) {
          console.log(`✅ ${fix.name} 成功`);
          
          // 等待一下檢查是否修復了問題
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          const video = document.querySelector('video') as HTMLVideoElement;
          if (video && video.readyState >= 3 && !video.paused) {
            console.log('🎉 問題已修復！');
            setIsFixing(false);
            return;
          }
        } else {
          console.log(`❌ ${fix.name} 失敗`);
        }
      } catch (error) {
        console.error(`❌ ${fix.name} 錯誤:`, error);
      }
      
      // 在嘗試之間等待
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('⚠️ 自動修復序列完成，但問題可能仍然存在');
    setIsFixing(false);
  };

  // 檢查是否應該顯示快速修復
  if (!isTargetChannel && !isVisible) {
    return null;
  }

  const canRunAutoFix = !isFixing && (!lastFixTime || Date.now() - lastFixTime > 30000);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-red-700 transition-colors z-50 animate-pulse"
      >
        🚑 快速修復
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-xl max-w-sm w-full z-50">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold text-red-400">🚑 快速修復</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      <div className="mb-4 text-sm text-gray-300">
        {isTargetChannel ? (
          <p>檢測到中視新聞台，提供專門的修復選項</p>
        ) : (
          <p>通用播放問題修復工具</p>
        )}
      </div>

      {/* 自動修復 */}
      <div className="mb-4">
        <Button
          onClick={runAutoFix}
          disabled={!canRunAutoFix}
          className={`w-full ${isFixing ? 'bg-yellow-600' : 'bg-red-600 hover:bg-red-700'}`}
        >
          {isFixing ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              修復中...
            </span>
          ) : (
            '🚀 一鍵自動修復'
          )}
        </Button>
        
        {!canRunAutoFix && lastFixTime && (
          <p className="text-xs text-gray-400 mt-1">
            請等待 {Math.ceil((30000 - (Date.now() - lastFixTime)) / 1000)} 秒後再試
          </p>
        )}
      </div>

      {/* 手動修復選項 */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-blue-400">手動修復選項:</h4>
        
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            onClick={quickFixes.reloadSegment}
            disabled={isFixing}
            className="text-xs"
          >
            🔄 重載片段
          </Button>
          
          <Button
            size="sm"
            onClick={quickFixes.clearBuffer}
            disabled={isFixing}
            className="text-xs"
          >
            🧹 清理緩衝
          </Button>
          
          <Button
            size="sm"
            onClick={quickFixes.lowerQuality}
            disabled={isFixing || !hlsInstance || hlsInstance.levels.length <= 1}
            className="text-xs"
          >
            📉 降低品質
          </Button>
          
          <Button
            size="sm"
            onClick={quickFixes.skipStall}
            disabled={isFixing}
            className="text-xs"
          >
            ⏭️ 跳過卡頓
          </Button>
        </div>
        
        <Button
          onClick={quickFixes.reconnect}
          disabled={isFixing}
          className="w-full text-xs bg-orange-600 hover:bg-orange-700"
        >
          🔄 重新連接 (重載頁面)
        </Button>
      </div>

      {lastFixTime && (
        <div className="mt-3 pt-2 border-t border-gray-700 text-xs text-gray-400">
          上次修復: {new Date(lastFixTime).toLocaleTimeString()}
        </div>
      )}
    </div>
  );
};
