import React, { useState, useEffect, useRef } from 'react';
import { usePlayerStore } from '../../stores/playerStore';
import { Button } from '../ui/Button';

interface NetworkStats {
  downloadSpeed: number;
  latency: number;
  packetLoss: number;
  jitter: number;
}

interface BufferStats {
  currentBuffer: number;
  targetBuffer: number;
  bufferHealth: 'critical' | 'low' | 'good' | 'excellent';
  stallCount: number;
  lastStallTime?: number;
}

interface QualityStats {
  currentLevel: number;
  availableLevels: number;
  switchCount: number;
  droppedFrames: number;
  totalFrames: number;
}

export const AdvancedDiagnostics: React.FC = () => {
  const { hlsInstance, currentChannel, isPlaying } = usePlayerStore();
  const [isVisible, setIsVisible] = useState(false);
  const [networkStats, setNetworkStats] = useState<NetworkStats>({
    downloadSpeed: 0,
    latency: 0,
    packetLoss: 0,
    jitter: 0
  });
  const [bufferStats, setBufferStats] = useState<BufferStats>({
    currentBuffer: 0,
    targetBuffer: 30,
    bufferHealth: 'good',
    stallCount: 0
  });
  const [qualityStats, setQualityStats] = useState<QualityStats>({
    currentLevel: -1,
    availableLevels: 0,
    switchCount: 0,
    droppedFrames: 0,
    totalFrames: 0
  });
  const [logs, setLogs] = useState<string[]>([]);
  const stallCountRef = useRef(0);
  const lastBufferTimeRef = useRef(0);
  const qualitySwitchCountRef = useRef(0);

  // 添加日誌
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  // 監控緩衝狀態
  useEffect(() => {
    if (!isVisible || !isPlaying) return;

    const interval = setInterval(() => {
      const video = document.querySelector('video') as HTMLVideoElement;
      if (!video) return;

      // 緩衝統計
      if (video.buffered.length > 0) {
        const currentTime = video.currentTime;
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const currentBuffer = bufferedEnd - currentTime;

        let bufferHealth: BufferStats['bufferHealth'] = 'good';
        if (currentBuffer < 2) bufferHealth = 'critical';
        else if (currentBuffer < 5) bufferHealth = 'low';
        else if (currentBuffer > 15) bufferHealth = 'excellent';

        // 檢測卡頓
        if (video.readyState < 3 && !video.paused) {
          stallCountRef.current++;
          addLog(`⚠️ 檢測到卡頓 #${stallCountRef.current}`);
        }

        setBufferStats(prev => ({
          ...prev,
          currentBuffer,
          bufferHealth,
          stallCount: stallCountRef.current,
          lastStallTime: video.readyState < 3 ? Date.now() : prev.lastStallTime
        }));

        // 品質統計
        if (hlsInstance) {
          const currentLevel = hlsInstance.currentLevel;
          const availableLevels = hlsInstance.levels?.length || 0;
          
          // 檢測品質切換
          if (currentLevel !== qualityStats.currentLevel && currentLevel !== -1) {
            qualitySwitchCountRef.current++;
            const level = hlsInstance.levels?.[currentLevel];
            addLog(`🔄 品質切換到 Level ${currentLevel} (${level?.height || '?'}p, ${Math.round((level?.bitrate || 0) / 1000)}kbps)`);
          }

          // 獲取丟幀統計
          // @ts-ignore
          const quality = video.getVideoPlaybackQuality?.();
          const droppedFrames = quality?.droppedVideoFrames || 0;
          const totalFrames = quality?.totalVideoFrames || 0;

          setQualityStats({
            currentLevel,
            availableLevels,
            switchCount: qualitySwitchCountRef.current,
            droppedFrames,
            totalFrames
          });
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible, isPlaying, hlsInstance, qualityStats.currentLevel]);

  // 網路測試
  const testNetwork = async () => {
    if (!currentChannel) return;

    addLog('🌐 開始網路測試...');
    const startTime = Date.now();

    try {
      // 測試延遲
      const response = await fetch(currentChannel.url, { method: 'HEAD' });
      const latency = Date.now() - startTime;

      // 簡單的下載速度測試
      const downloadStart = Date.now();
      const testResponse = await fetch(currentChannel.url);
      const downloadTime = Date.now() - downloadStart;
      const contentLength = parseInt(testResponse.headers.get('content-length') || '0');
      const downloadSpeed = contentLength > 0 ? (contentLength / downloadTime) * 1000 : 0; // bytes/sec

      setNetworkStats({
        downloadSpeed: downloadSpeed / 1024, // KB/s
        latency,
        packetLoss: 0, // 瀏覽器無法直接測量
        jitter: 0 // 需要多次測試計算
      });

      addLog(`✅ 網路測試完成 - 延遲: ${latency}ms, 速度: ${Math.round(downloadSpeed / 1024)}KB/s`);
    } catch (error) {
      addLog(`❌ 網路測試失敗: ${error}`);
    }
  };

  // 強制品質調整
  const forceQuality = (level: number) => {
    if (!hlsInstance) return;
    
    hlsInstance.currentLevel = level;
    const levelInfo = hlsInstance.levels?.[level];
    addLog(`🎯 手動設定品質到 Level ${level} (${levelInfo?.height || '?'}p)`);
  };

  // 清理緩衝
  const clearBuffer = () => {
    const video = document.querySelector('video') as HTMLVideoElement;
    if (!video || !hlsInstance) return;

    try {
      // 重新載入當前片段
      hlsInstance.trigger('hlsBufferFlushed');
      addLog('🧹 已清理緩衝區');
    } catch (error) {
      addLog(`❌ 清理緩衝失敗: ${error}`);
    }
  };

  // 重新連接
  const reconnect = () => {
    if (!hlsInstance || !currentChannel) return;

    addLog('🔄 重新連接中...');
    hlsInstance.destroy();
    
    // 重新初始化會由 HLSPlayer 組件處理
    setTimeout(() => {
      addLog('✅ 重新連接完成');
    }, 2000);
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-20 right-4 bg-red-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-red-700 transition-colors z-50"
      >
        高級診斷
      </button>
    );
  }

  return (
    <div className="fixed top-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto z-50">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-red-400">高級診斷</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      {/* 緩衝狀態 */}
      <div className="mb-4 p-3 bg-gray-800 rounded">
        <h4 className="font-medium mb-2 text-blue-400">緩衝狀態</h4>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span>當前緩衝:</span>
            <span className={`${
              bufferStats.bufferHealth === 'critical' ? 'text-red-400' :
              bufferStats.bufferHealth === 'low' ? 'text-yellow-400' :
              bufferStats.bufferHealth === 'excellent' ? 'text-green-400' : 'text-blue-400'
            }`}>
              {bufferStats.currentBuffer.toFixed(1)}s
            </span>
          </div>
          <div className="flex justify-between">
            <span>健康度:</span>
            <span className={`${
              bufferStats.bufferHealth === 'critical' ? 'text-red-400' :
              bufferStats.bufferHealth === 'low' ? 'text-yellow-400' :
              bufferStats.bufferHealth === 'excellent' ? 'text-green-400' : 'text-blue-400'
            }`}>
              {bufferStats.bufferHealth}
            </span>
          </div>
          <div className="flex justify-between">
            <span>卡頓次數:</span>
            <span className="text-red-400">{bufferStats.stallCount}</span>
          </div>
        </div>
      </div>

      {/* 品質狀態 */}
      <div className="mb-4 p-3 bg-gray-800 rounded">
        <h4 className="font-medium mb-2 text-purple-400">品質狀態</h4>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span>當前品質:</span>
            <span className="text-purple-400">
              Level {qualityStats.currentLevel} / {qualityStats.availableLevels - 1}
            </span>
          </div>
          <div className="flex justify-between">
            <span>切換次數:</span>
            <span className="text-yellow-400">{qualityStats.switchCount}</span>
          </div>
          {qualityStats.totalFrames > 0 && (
            <div className="flex justify-between">
              <span>丟幀率:</span>
              <span className={qualityStats.droppedFrames > 0 ? 'text-red-400' : 'text-green-400'}>
                {((qualityStats.droppedFrames / qualityStats.totalFrames) * 100).toFixed(2)}%
              </span>
            </div>
          )}
        </div>
      </div>

      {/* 網路狀態 */}
      <div className="mb-4 p-3 bg-gray-800 rounded">
        <div className="flex justify-between items-center mb-2">
          <h4 className="font-medium text-green-400">網路狀態</h4>
          <Button size="sm" onClick={testNetwork} className="text-xs">
            測試
          </Button>
        </div>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span>延遲:</span>
            <span className="text-green-400">{networkStats.latency}ms</span>
          </div>
          <div className="flex justify-between">
            <span>下載速度:</span>
            <span className="text-green-400">{Math.round(networkStats.downloadSpeed)}KB/s</span>
          </div>
        </div>
      </div>

      {/* 控制按鈕 */}
      <div className="mb-4 space-y-2">
        <div className="grid grid-cols-2 gap-2">
          <Button size="sm" onClick={clearBuffer} className="text-xs">
            清理緩衝
          </Button>
          <Button size="sm" onClick={reconnect} className="text-xs">
            重新連接
          </Button>
        </div>
        
        {/* 品質控制 */}
        {hlsInstance && hlsInstance.levels && (
          <div>
            <div className="text-xs text-gray-400 mb-1">手動品質:</div>
            <div className="grid grid-cols-3 gap-1">
              <Button size="sm" onClick={() => forceQuality(-1)} className="text-xs">
                自動
              </Button>
              {hlsInstance.levels.map((level, index) => (
                <Button
                  key={index}
                  size="sm"
                  onClick={() => forceQuality(index)}
                  className={`text-xs ${qualityStats.currentLevel === index ? 'bg-blue-600' : ''}`}
                >
                  {level.height || index}p
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 日誌 */}
      <div className="p-3 bg-gray-800 rounded">
        <h4 className="font-medium mb-2 text-yellow-400">診斷日誌</h4>
        <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-gray-300">
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
