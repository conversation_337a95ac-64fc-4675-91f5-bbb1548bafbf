import { Component, type ErrorInfo, type ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg p-6">
            <div className="text-center mb-6">
              <div className="text-6xl mb-4">💥</div>
              <h1 className="text-2xl font-bold text-red-600 mb-2">
                應用程序發生錯誤
              </h1>
              <p className="text-gray-600">
                很抱歉，應用程序遇到了一個意外錯誤。
              </p>
            </div>

            {this.state.error && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-2 text-gray-800">
                  錯誤詳情：
                </h2>
                <div className="bg-red-100 border border-red-400 rounded p-4">
                  <p className="text-red-800 font-mono text-sm">
                    {this.state.error.message}
                  </p>
                </div>
              </div>
            )}

            {this.state.errorInfo && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-2 text-gray-800">
                  堆疊追蹤：
                </h2>
                <div className="bg-gray-100 border rounded p-4 max-h-64 overflow-auto">
                  <pre className="text-xs text-gray-700">
                    {this.state.errorInfo.componentStack}
                  </pre>
                </div>
              </div>
            )}

            <div className="flex flex-wrap gap-4 justify-center">
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                🔄 重新載入頁面
              </button>
              
              <button
                onClick={() => window.location.href = '/'}
                className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                🏠 返回首頁
              </button>
              
              <button
                onClick={() => window.location.href = '/debug'}
                className="px-6 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
              >
                🔍 查看調試信息
              </button>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
              <h3 className="font-semibold text-yellow-800 mb-2">
                🛠️ 開發者信息：
              </h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• 檢查瀏覽器控制台以獲取更多詳細信息</li>
                <li>• 確保所有依賴項都已正確安裝</li>
                <li>• 檢查網路連接和 API 端點</li>
                <li>• 嘗試清除瀏覽器緩存和 localStorage</li>
              </ul>
            </div>

            <div className="mt-4 text-center text-sm text-gray-500">
              <p>
                如果問題持續存在，請聯繫技術支援或查看項目文檔。
              </p>
              <p className="mt-1">
                錯誤時間：{new Date().toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
