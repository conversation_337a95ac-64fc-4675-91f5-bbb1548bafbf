import React from 'react';

export const DebugPage: React.FC = () => {
  const debugInfo = {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    origin: window.location.origin,
    pathname: window.location.pathname,
    search: window.location.search,
    hash: window.location.hash,
    baseURI: document.baseURI,
    environment: {
      NODE_ENV: import.meta.env.MODE,
      BASE_URL: import.meta.env.BASE_URL,
      DEV: import.meta.env.DEV,
      PROD: import.meta.env.PROD,
      SSR: import.meta.env.SSR,
    },
    viteEnv: import.meta.env,
    reactVersion: React.version,
    features: {
      localStorage: typeof Storage !== 'undefined',
      sessionStorage: typeof sessionStorage !== 'undefined',
      webGL: !!window.WebGLRenderingContext,
      webWorker: typeof Worker !== 'undefined',
      fetch: typeof fetch !== 'undefined',
      promises: typeof Promise !== 'undefined',
      modules: typeof window !== 'undefined',
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🔍 TVBOX 調試信息
          </h1>
          
          <div className="mb-6 p-4 bg-green-100 border border-green-400 rounded">
            <h2 className="text-lg font-semibold text-green-800 mb-2">
              ✅ 應用程序正常運行
            </h2>
            <p className="text-green-700">
              如果您能看到這個頁面，說明 React 應用程序已經成功載入和渲染。
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 環境信息 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                🌍 環境信息
              </h3>
              <div className="space-y-2 text-sm">
                <div><strong>模式:</strong> {debugInfo.environment.NODE_ENV}</div>
                <div><strong>Base URL:</strong> {debugInfo.environment.BASE_URL}</div>
                <div><strong>開發模式:</strong> {debugInfo.environment.DEV ? '是' : '否'}</div>
                <div><strong>生產模式:</strong> {debugInfo.environment.PROD ? '是' : '否'}</div>
                <div><strong>SSR:</strong> {debugInfo.environment.SSR ? '是' : '否'}</div>
              </div>
            </div>

            {/* URL 信息 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                🔗 URL 信息
              </h3>
              <div className="space-y-2 text-sm break-all">
                <div><strong>完整 URL:</strong> {debugInfo.url}</div>
                <div><strong>Origin:</strong> {debugInfo.origin}</div>
                <div><strong>路徑:</strong> {debugInfo.pathname}</div>
                <div><strong>查詢參數:</strong> {debugInfo.search || '無'}</div>
                <div><strong>Hash:</strong> {debugInfo.hash || '無'}</div>
                <div><strong>Base URI:</strong> {debugInfo.baseURI}</div>
              </div>
            </div>

            {/* 瀏覽器功能 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                🌐 瀏覽器功能
              </h3>
              <div className="space-y-2 text-sm">
                {Object.entries(debugInfo.features).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span>{key}:</span>
                    <span className={value ? 'text-green-600' : 'text-red-600'}>
                      {value ? '✅' : '❌'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* 技術信息 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                ⚙️ 技術信息
              </h3>
              <div className="space-y-2 text-sm">
                <div><strong>React 版本:</strong> {debugInfo.reactVersion}</div>
                <div><strong>時間戳:</strong> {debugInfo.timestamp}</div>
                <div><strong>User Agent:</strong> 
                  <div className="mt-1 text-xs break-all bg-white p-2 rounded">
                    {debugInfo.userAgent}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Vite 環境變量 */}
          <div className="mt-6 bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">
              🔧 Vite 環境變量
            </h3>
            <pre className="text-xs bg-white p-3 rounded overflow-auto">
              {JSON.stringify(debugInfo.viteEnv, null, 2)}
            </pre>
          </div>

          {/* 操作按鈕 */}
          <div className="mt-6 flex flex-wrap gap-4">
            <button
              onClick={() => window.location.href = '/'}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              🏠 返回首頁
            </button>
            
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              🔄 重新載入
            </button>
            
            <button
              onClick={() => {
                const data = JSON.stringify(debugInfo, null, 2);
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'tvbox-debug-info.json';
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              💾 下載調試信息
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
