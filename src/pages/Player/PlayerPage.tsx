import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  ListBulletIcon,
  HeartIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { useAppStore } from '../../stores/appStore';
import { usePlayerStore } from '../../stores/playerStore';
import { HLSPlayer } from '../../components/Player/HLSPlayer';
import { PlayerControls } from '../../components/Player/PlayerControls';
import { PlayerDiagnostics } from '../../components/Player/PlayerDiagnostics';
import { AdvancedDiagnostics } from '../../components/Player/AdvancedDiagnostics';
import { StallDetector } from '../../components/Player/StallDetector';
import { QuickFix } from '../../components/Player/QuickFix';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { ErrorMessage } from '../../components/ui/ErrorMessage';

export const PlayerPage: React.FC = () => {
  const navigate = useNavigate();
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  const {
    currentPlaylist,
    addFavoriteChannel,
    removeFavoriteChannel,
    isFavoriteChannel,
    settings
  } = useAppStore();
  const {
    currentChannel,
    isLoading,
    error,
    clearError
  } = usePlayerStore();

  const isFavorite = currentChannel ? isFavoriteChannel(currentChannel.id) : false;

  // 添加調試日誌
  useEffect(() => {
    console.log('PlayerPage 載入，當前頻道:', currentChannel?.name || 'null');
  }, [currentChannel]);

  useEffect(() => {
    // 只在組件卸載時清理播放器狀態，但保留當前頻道
    return () => {
      // 不要重置 currentChannel，只清理播放狀態
      const { destroyHlsInstance, setLoading, clearError } = usePlayerStore.getState();
      destroyHlsInstance();
      setLoading(false);
      clearError();
    };
  }, []);

  const handleBackToChannels = () => {
    navigate('/channels');
  };

  const handleToggleFavorite = () => {
    if (!currentChannel) return;
    
    if (isFavorite) {
      removeFavoriteChannel(currentChannel.id);
    } else {
      addFavoriteChannel(currentChannel);
    }
  };

  // 如果沒有選擇頻道，重定向到頻道列表
  if (!currentChannel) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-yellow-400 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            沒有選擇頻道
          </h2>
          <p className="text-gray-600 mb-6">
            請先選擇一個頻道來播放
          </p>
          <Button 
            onClick={() => navigate(currentPlaylist ? '/channels' : '/')}
            leftIcon={<ListBulletIcon className="h-5 w-5" />}
          >
            {currentPlaylist ? '選擇頻道' : '回到首頁'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      {/* 頂部控制欄 */}
      <div className="bg-gray-900 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={handleBackToChannels}
              className="text-white hover:bg-gray-800"
              leftIcon={<ArrowLeftIcon className="h-5 w-5" />}
            >
              返回頻道列表
            </Button>
            
            <div>
              <h1 className="text-lg font-medium">{currentChannel.name}</h1>
              {currentChannel.group && (
                <p className="text-sm text-gray-400">{currentChannel.group}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* 收藏按鈕 */}
            <Button
              variant="ghost"
              onClick={handleToggleFavorite}
              className="text-white hover:bg-gray-800"
            >
              {isFavorite ? (
                <HeartSolidIcon className="h-5 w-5 text-red-500" />
              ) : (
                <HeartIcon className="h-5 w-5" />
              )}
            </Button>

            {/* 頻道資訊 */}
            {currentChannel.resolution && (
              <span className="px-2 py-1 bg-gray-700 text-xs rounded">
                {currentChannel.resolution}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 播放器區域 */}
      <div className="relative">
        {/* 錯誤訊息 */}
        {error && (
          <div className="absolute top-4 left-4 right-4 z-10">
            <ErrorMessage
              message={error}
              onDismiss={clearError}
              variant="error"
            />
          </div>
        )}

        {/* 載入指示器 */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center z-10 bg-black bg-opacity-50">
            <LoadingSpinner size="lg" text="正在載入影片..." />
          </div>
        )}

        {/* 播放器 */}
        <div className="aspect-video bg-black">
          <HLSPlayer
            channel={currentChannel}
            autoplay={settings.autoplay}
            className="w-full h-full"
          />
        </div>

        {/* 播放器控制 */}
        <PlayerControls />

        {/* 診斷工具 */}
        <PlayerDiagnostics
          isVisible={showDiagnostics}
          onToggle={() => setShowDiagnostics(!showDiagnostics)}
        />

        {/* 高級診斷工具 */}
        <AdvancedDiagnostics />

        {/* 卡頓檢測器 */}
        <StallDetector autoRecover={true} />

        {/* 快速修復工具 */}
        <QuickFix
          channelName={currentChannel.name}
          channelUrl={currentChannel.url}
        />
      </div>

      {/* 頻道詳細資訊 */}
      <div className="bg-gray-900 text-white p-4">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 基本資訊 */}
            <div>
              <h3 className="text-lg font-medium mb-3">頻道資訊</h3>
              <dl className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <dt className="text-gray-400">頻道名稱:</dt>
                  <dd>{currentChannel.name}</dd>
                </div>
                {currentChannel.group && (
                  <div className="flex justify-between">
                    <dt className="text-gray-400">群組:</dt>
                    <dd>{currentChannel.group}</dd>
                  </div>
                )}
                {currentChannel.language && (
                  <div className="flex justify-between">
                    <dt className="text-gray-400">語言:</dt>
                    <dd>{currentChannel.language}</dd>
                  </div>
                )}
                {currentChannel.resolution && (
                  <div className="flex justify-between">
                    <dt className="text-gray-400">解析度:</dt>
                    <dd>{currentChannel.resolution}</dd>
                  </div>
                )}
              </dl>
            </div>

            {/* 技術資訊 */}
            <div>
              <h3 className="text-lg font-medium mb-3">技術資訊</h3>
              <dl className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <dt className="text-gray-400">串流 URL:</dt>
                  <dd className="truncate max-w-xs" title={currentChannel.url}>
                    {currentChannel.url}
                  </dd>
                </div>
                {currentChannel.tvgId && (
                  <div className="flex justify-between">
                    <dt className="text-gray-400">TVG ID:</dt>
                    <dd>{currentChannel.tvgId}</dd>
                  </div>
                )}
                {currentChannel.tvgName && (
                  <div className="flex justify-between">
                    <dt className="text-gray-400">TVG Name:</dt>
                    <dd>{currentChannel.tvgName}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
