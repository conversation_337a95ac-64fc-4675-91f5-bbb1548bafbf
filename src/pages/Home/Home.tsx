import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  LinkIcon,
  QrCodeIcon,
  ClockIcon,
  TrashIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { useAppStore } from '../../stores/appStore';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { ErrorMessage } from '../../components/ui/ErrorMessage';
import { QRScanner } from '../../components/QRScanner/QRScanner';

export const Home: React.FC = () => {
  const navigate = useNavigate();
  const {
    recentPlaylists,
    isLoading,
    error,
    loadPlaylistFromUrl,
    removeRecentPlaylist,
    clearError
  } = useAppStore();

  const [playlistUrl, setPlaylistUrl] = useState('');
  const [isQRScannerOpen, setIsQRScannerOpen] = useState(false);
  const [urlError, setUrlError] = useState('');

  const validateUrl = (url: string): boolean => {
    try {
      // 清理 URL，移除可能的空白字符
      const cleanUrl = url.trim();

      // 基本格式檢查
      if (!cleanUrl || (!cleanUrl.startsWith('http://') && !cleanUrl.startsWith('https://'))) {
        return false;
      }

      // 嘗試創建 URL 對象來驗證格式
      new URL(cleanUrl);
      return true;
    } catch (error) {
      // 如果 URL 構造失敗，嘗試更寬鬆的驗證
      try {
        const cleanUrl = url.trim();
        // 簡單的正則表達式驗證
        const urlPattern = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;
        return urlPattern.test(cleanUrl);
      } catch {
        return false;
      }
    }
  };

  const handleLoadPlaylist = async () => {
    const cleanUrl = playlistUrl.trim();

    if (!cleanUrl) {
      setUrlError('請輸入播放清單 URL');
      return;
    }

    if (!validateUrl(cleanUrl)) {
      setUrlError('請輸入有效的 URL');
      return;
    }

    setUrlError('');
    await loadPlaylistFromUrl(cleanUrl);

    // 如果載入成功，導航到頻道列表頁面
    if (!error) {
      navigate('/channels');
    }
  };

  const handleQRScanSuccess = async (decodedText: string) => {
    const cleanUrl = decodedText.trim();
    setPlaylistUrl(cleanUrl);
    setIsQRScannerOpen(false);

    if (validateUrl(cleanUrl)) {
      await loadPlaylistFromUrl(cleanUrl);
      if (!error) {
        navigate('/channels');
      }
    } else {
      setUrlError('掃描到的內容不是有效的 URL');
    }
  };

  const handleRecentPlaylistClick = async (playlistUrl: string) => {
    await loadPlaylistFromUrl(playlistUrl);
    if (!error) {
      navigate('/channels');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLoadPlaylist();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 標題 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">TVBOX</h1>
          <p className="text-lg text-gray-600">
            IPTV 串流播放平台
          </p>
        </div>

        {/* 主要輸入區域 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            載入播放清單
          </h2>

          <div className="space-y-4">
            <Input
              label="播放清單 URL"
              placeholder="請輸入 .m3u 或 .m3u8 播放清單的 URL"
              value={playlistUrl}
              onChange={(e) => {
                setPlaylistUrl(e.target.value);
                setUrlError('');
              }}
              onKeyPress={handleKeyPress}
              error={urlError}
              leftIcon={<LinkIcon className="h-5 w-5" />}
              helperText="支援 GitHub raw 連結、直播源等"
            />

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={handleLoadPlaylist}
                isLoading={isLoading}
                className="flex-1"
                leftIcon={<PlayIcon className="h-5 w-5" />}
              >
                載入播放清單
              </Button>

              <Button
                variant="outline"
                onClick={() => setIsQRScannerOpen(true)}
                leftIcon={<QrCodeIcon className="h-5 w-5" />}
              >
                掃描 QR 碼
              </Button>
            </div>
          </div>

          {/* 錯誤訊息 */}
          {error && (
            <div className="mt-4">
              <ErrorMessage
                message={error}
                onDismiss={clearError}
              />
              {/* CORS 相關錯誤的額外提示 */}
              {(error.includes('CORS') || error.includes('跨域') || error.includes('Access-Control')) && (
                <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-800">
                    <strong>💡 解決建議：</strong>
                  </p>
                  <ul className="text-sm text-blue-700 mt-1 space-y-1">
                    <li>• 我們正在使用 CORS 代理自動解決此問題</li>
                    <li>• 請稍等片刻，系統會自動重試</li>
                    <li>• 如果問題持續，請嘗試其他播放清單 URL</li>
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* 載入指示器 */}
          {isLoading && (
            <div className="mt-4">
              <LoadingSpinner text="正在載入播放清單..." />
              <div className="mt-2 text-sm text-gray-600 text-center">
                如果是第一次載入，可能需要較長時間...
              </div>
            </div>
          )}
        </div>

        {/* 最近使用的播放清單 */}
        {recentPlaylists.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                最近使用
              </h2>
            </div>

            <div className="space-y-3">
              {recentPlaylists.map((playlist) => (
                <div
                  key={playlist.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div
                    className="flex-1 cursor-pointer"
                    onClick={() => handleRecentPlaylistClick(playlist.url)}
                  >
                    <h3 className="font-medium text-gray-900">
                      {playlist.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {playlist.totalChannels} 個頻道 •
                      {new Date(playlist.lastUpdated).toLocaleDateString('zh-TW')}
                    </p>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeRecentPlaylist(playlist.id)}
                    className="text-gray-400 hover:text-red-600"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* QR 掃描器 */}
        <QRScanner
          isOpen={isQRScannerOpen}
          onClose={() => setIsQRScannerOpen(false)}
          onScanSuccess={handleQRScanSuccess}
        />
      </div>
    </div>
  );
};
