# TVBOX - IPTV 串流播放平台

TVBOX 是一款針對 IPTV 使用者設計的現代化影音串流播放平台，支援 .m3u / .m3u8 等格式的直播清單。

## ✨ 功能特色

### 🎯 核心功能
- 🎥 **多格式支援**: 支援 m3u、m3u8、HLS 等常見串流格式
- 📱 **響應式設計**: 適配桌面和行動裝置
- 🔍 **智慧搜尋**: 快速搜尋和過濾頻道
- ❤️ **收藏功能**: 收藏喜愛的頻道
- 📋 **播放清單管理**: 管理多個播放清單
- 📱 **QR 掃描**: 快速匯入播放清單
- 🎨 **現代化 UI**: 使用 TailwindCSS 設計
- 💾 **本地儲存**: 自動儲存設定和收藏

### 🚑 專門針對中視新聞台卡頓問題的解決方案
- 🎯 **智能頻道優化** - 針對特定頻道的自動優化配置
- 📈 **高級診斷工具** - 實時監控緩衝、品質和網路狀況
- 🔍 **卡頓檢測系統** - 自動檢測播放卡頓並記錄統計
- ⚡ **一鍵快速修復** - 自動修復序列和手動修復選項
- 🛠️ **自動恢復機制** - 多層次的播放問題自動恢復
- 🌐 **網路狀況檢測** - 自動調整配置以適應網路環境
- 📊 **詳細診斷日誌** - 完整的播放事件記錄

## 🚀 快速開始

### 安裝依賴
```bash
npm install
```

### 啟動開發伺服器
```bash
npm run dev
```

### 建構生產版本
```bash
npm run build
```

## 📖 使用說明

### 1. 載入播放清單
- 在首頁輸入 .m3u 或 .m3u8 播放清單的 URL
- 支援 GitHub raw 連結、直播源等
- 或使用 QR 掃描功能快速匯入

### 2. 瀏覽頻道
- 載入成功後會顯示所有可用頻道
- 支援按群組分類和搜尋功能
- 點擊頻道卡片即可開始播放

### 3. 播放控制
- 支援播放/暫停、音量調節
- 全螢幕播放模式
- 進度條控制

### 4. 收藏管理
- 點擊愛心圖示收藏頻道
- 在設定頁面查看收藏統計

### 5. 中視新聞台優化功能使用
- **自動優化**: 系統會自動檢測中視新聞台並應用優化配置
- **監控工具**:
  - 點擊右下角的"高級診斷"查看詳細狀態
  - 卡頓監控面板會顯示實時統計
- **問題修復**:
  - 如果遇到卡頓，會出現紅色的"🚑 快速修復"按鈕
  - 點擊"🚀 一鍵自動修復"進行自動修復
  - 或使用手動修復選項進行精確控制

## 🛠️ 技術棧

- **前端框架**: React 18 + TypeScript
- **建構工具**: Vite
- **UI 框架**: TailwindCSS + Headless UI
- **播放器**: HLS.js
- **狀態管理**: Zustand
- **路由**: React Router
- **QR 掃描**: html5-qrcode
- **圖示**: Heroicons

## 📁 專案結構

```
src/
├── components/          # 可重用組件
│   ├── ui/             # 基礎 UI 組件
│   ├── Player/         # 播放器組件
│   ├── ChannelList/    # 頻道列表組件
│   └── QRScanner/      # QR 掃描組件
├── pages/              # 頁面組件
│   ├── Home/           # 首頁
│   ├── ChannelList/    # 頻道列表頁
│   ├── Player/         # 播放頁面
│   └── Settings/       # 設定頁面
├── services/           # 業務邏輯服務
│   ├── m3uParser.ts    # M3U 解析服務
│   ├── storage.ts      # 本地儲存服務
│   └── api.ts          # API 服務
├── stores/             # 狀態管理
├── types/              # TypeScript 類型定義
└── utils/              # 工具函數
```

## 🧪 測試

專案包含一個測試用的 M3U 檔案：
- 訪問: `http://localhost:5173/sample.m3u`
- 包含 3 個測試頻道用於功能驗證

## 🔧 開發

### 新增功能
1. 在對應的 `components/` 或 `pages/` 目錄下建立組件
2. 更新路由配置（如需要）
3. 更新狀態管理（如需要）

### 樣式開發
- 使用 TailwindCSS 工具類
- 自定義樣式在 `src/index.css` 中定義
- 響應式設計優先

## 📱 未來計劃

- [ ] Android APK 版本
- [ ] 離線播放功能
- [ ] 多語言支援
- [ ] 主題切換
- [ ] 播放歷史記錄
- [ ] 頻道分享功能

## 🚀 部署到 GitHub

### 推送代碼到 GitHub

由於需要身份驗證，請按照以下步驟操作：

#### 方法 1: 使用個人訪問令牌 (推薦)

1. **創建個人訪問令牌**:
   - 訪問 [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
   - 點擊 "Generate new token (classic)"
   - 選擇適當的權限 (至少需要 `repo` 權限)
   - 複製生成的令牌

2. **推送代碼**:
```bash
git push -u origin main
# 當提示輸入用戶名時，輸入你的 GitHub 用戶名
# 當提示輸入密碼時，輸入你的個人訪問令牌 (不是 GitHub 密碼)
```

#### 方法 2: 使用 SSH 密鑰

1. **生成 SSH 密鑰** (如果還沒有):
```bash
ssh-keygen -t ed25519 -C "<EMAIL>"
```

2. **添加 SSH 密鑰到 GitHub**:
   - 複製公鑰內容: `cat ~/.ssh/id_ed25519.pub`
   - 訪問 [GitHub Settings > SSH and GPG keys](https://github.com/settings/keys)
   - 點擊 "New SSH key" 並粘貼公鑰

3. **使用 SSH URL 推送**:
```bash
git remote set-<NAME_EMAIL>:johnyarcher2100/TVBox.git
git push -u origin main
```

### 自動部署到 GitHub Pages

項目已配置為可以部署到 GitHub Pages。推送代碼後，可以在倉庫設置中啟用 GitHub Pages。

## 🐛 故障排除

### 常見問題

1. **播放卡頓**:
   - 使用右下角的"快速修復"工具
   - 檢查網路連接
   - 嘗試降低播放品質

2. **頻道無法播放**:
   - 檢查 M3U 文件格式
   - 確認頻道 URL 有效
   - 查看診斷工具中的錯誤信息

3. **CORS 錯誤**:
   - 某些頻道可能需要代理服務器
   - 檢查瀏覽器控制台的錯誤信息

## 📄 授權

MIT License
