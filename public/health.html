<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TVBOX 健康檢查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 TVBOX 健康檢查</h1>
        
        <div class="status success">
            ✅ 靜態文件部署成功！如果您能看到這個頁面，說明 Netlify 部署基本正常。
        </div>
        
        <div class="status info">
            📊 部署信息：
            <ul>
                <li><strong>時間：</strong><span id="timestamp"></span></li>
                <li><strong>URL：</strong><span id="current-url"></span></li>
                <li><strong>User Agent：</strong><span id="user-agent"></span></li>
            </ul>
        </div>
        
        <h2>🔗 快速導航</h2>
        <a href="/" class="button">🏠 返回主應用</a>
        <a href="/debug" class="button">🔍 調試頁面</a>
        <a href="javascript:location.reload()" class="button">🔄 重新載入</a>
        
        <h2>📋 檢查清單</h2>
        <ul>
            <li>✅ 靜態文件部署正常</li>
            <li id="js-check">⏳ 檢查 JavaScript 支持...</li>
            <li id="react-check">⏳ 檢查 React 應用...</li>
            <li id="router-check">⏳ 檢查路由功能...</li>
        </ul>
        
        <h2>🛠️ 故障排除</h2>
        <p>如果主應用無法載入，請嘗試：</p>
        <ol>
            <li>清除瀏覽器緩存</li>
            <li>檢查瀏覽器控制台錯誤</li>
            <li>嘗試無痕模式</li>
            <li>檢查網路連接</li>
        </ol>
    </div>

    <script>
        // 更新時間戳
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        
        // 檢查 JavaScript
        document.getElementById('js-check').innerHTML = '✅ JavaScript 支持正常';
        
        // 檢查 React 應用
        setTimeout(() => {
            fetch('/')
                .then(response => response.text())
                .then(html => {
                    if (html.includes('root')) {
                        document.getElementById('react-check').innerHTML = '✅ React 應用結構正常';
                    } else {
                        document.getElementById('react-check').innerHTML = '❌ React 應用結構異常';
                    }
                })
                .catch(() => {
                    document.getElementById('react-check').innerHTML = '❌ 無法載入主應用';
                });
        }, 1000);
        
        // 檢查路由
        setTimeout(() => {
            fetch('/debug')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('router-check').innerHTML = '✅ SPA 路由正常';
                    } else {
                        document.getElementById('router-check').innerHTML = '❌ 路由配置可能有問題';
                    }
                })
                .catch(() => {
                    document.getElementById('router-check').innerHTML = '❌ 路由測試失敗';
                });
        }, 2000);
    </script>
</body>
</html>
