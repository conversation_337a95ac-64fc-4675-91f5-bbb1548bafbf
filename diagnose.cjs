#!/usr/bin/env node

// 簡單的診斷腳本
console.log('🔍 TVBOX 診斷腳本');
console.log('================');

// 檢查 Node.js 環境
console.log('📊 環境信息:');
console.log(`Node.js: ${process.version}`);
console.log(`平台: ${process.platform}`);
console.log(`架構: ${process.arch}`);
console.log(`工作目錄: ${process.cwd()}`);

// 檢查環境變量
console.log('\n🌍 環境變量:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || '未設置'}`);
console.log(`NETLIFY: ${process.env.NETLIFY || '未設置'}`);

// 檢查文件
const fs = require('fs');
const path = require('path');

console.log('\n📁 文件檢查:');
const files = ['package.json', 'vite.config.ts', 'src/main.tsx', 'index.html'];
files.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
});

// 檢查 node_modules
console.log('\n📦 依賴檢查:');
const modules = ['vite', 'react', '@vitejs/plugin-react'];
modules.forEach(module => {
  const modulePath = path.join('node_modules', module);
  const exists = fs.existsSync(modulePath);
  console.log(`${exists ? '✅' : '❌'} ${module}`);
});

// 檢查 vite 可執行文件
const viteBin = path.join('node_modules', '.bin', 'vite');
const viteExists = fs.existsSync(viteBin);
console.log(`${viteExists ? '✅' : '❌'} vite 可執行文件`);

console.log('\n🎯 診斷完成');

if (!viteExists) {
  console.log('\n❌ 發現問題: vite 不可用');
  console.log('建議解決方案:');
  console.log('1. 運行 npm install');
  console.log('2. 檢查 package.json 中的依賴');
  console.log('3. 確保 vite 在 dependencies 中');
  process.exit(1);
} else {
  console.log('\n✅ 所有檢查通過');
  process.exit(0);
}
