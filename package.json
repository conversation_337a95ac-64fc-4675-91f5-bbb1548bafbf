{"name": "tvbox-abuji1000", "private": true, "version": "1.0.0", "type": "module", "description": "IPTV 串流播放平台 - 支援 m3u/m3u8 格式的現代化影音播放器", "keywords": ["iptv", "m3u", "m3u8", "hls", "streaming", "video", "player"], "author": "TVBOX Team", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "build-netlify": "npm install && vite build", "build-with-types": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@types/hls.js": "^0.13.3", "autoprefixer": "^10.4.21", "hls.js": "^1.6.2", "html5-qrcode": "^2.3.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.1", "tailwindcss": "^3.4.17", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.5.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rollup": "^4.41.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}